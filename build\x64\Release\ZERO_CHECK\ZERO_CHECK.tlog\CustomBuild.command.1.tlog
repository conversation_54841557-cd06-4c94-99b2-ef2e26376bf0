^C:\USERS\<USER>\DESKTOP\驱动漏洞\DLLTEST2\TEST\BUILD\CMAKEFILES\EF4086BAC901335D179A3A3171D8E0F1\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/驱动漏洞/dlltest2/test -BC:/Users/<USER>/Desktop/驱动漏洞/dlltest2/test/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/驱动漏洞/dlltest2/test/build/wnbios_dll_standalone.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
