﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{A14AE30A-0326-3229-BA68-A04F4424E891}"
	ProjectSection(ProjectDependencies) = postProject
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA} = {A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91} = {4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC} = {303C29E5-A09D-39F2-AEF9-22B88E690FAC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{BCE17EB7-AAC8-3337-9C70-373BF332A63E}"
	ProjectSection(ProjectDependencies) = postProject
		{A14AE30A-0326-3229-BA68-A04F4424E891} = {A14AE30A-0326-3229-BA68-A04F4424E891}
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA} = {A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "c_example", "c_example.vcxproj", "{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}"
	ProjectSection(ProjectDependencies) = postProject
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA} = {A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC} = {303C29E5-A09D-39F2-AEF9-22B88E690FAC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "wnbios_dll", "wnbios_dll.vcxproj", "{303C29E5-A09D-39F2-AEF9-22B88E690FAC}"
	ProjectSection(ProjectDependencies) = postProject
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA} = {A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A14AE30A-0326-3229-BA68-A04F4424E891}.Debug|x64.ActiveCfg = Debug|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.Debug|x64.Build.0 = Debug|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.Release|x64.ActiveCfg = Release|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.Release|x64.Build.0 = Release|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A14AE30A-0326-3229-BA68-A04F4424E891}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BCE17EB7-AAC8-3337-9C70-373BF332A63E}.Debug|x64.ActiveCfg = Debug|x64
		{BCE17EB7-AAC8-3337-9C70-373BF332A63E}.Release|x64.ActiveCfg = Release|x64
		{BCE17EB7-AAC8-3337-9C70-373BF332A63E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BCE17EB7-AAC8-3337-9C70-373BF332A63E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.Debug|x64.ActiveCfg = Debug|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.Debug|x64.Build.0 = Debug|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.Release|x64.ActiveCfg = Release|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.Release|x64.Build.0 = Release|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A5448A00-5C6D-3CA7-A90B-DF39D1710EDA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.Debug|x64.ActiveCfg = Debug|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.Debug|x64.Build.0 = Debug|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.Release|x64.ActiveCfg = Release|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.Release|x64.Build.0 = Release|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4C9BE8E5-D364-3B92-B2DA-E4E5B3AA4E91}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.Debug|x64.ActiveCfg = Debug|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.Debug|x64.Build.0 = Debug|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.Release|x64.ActiveCfg = Release|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.Release|x64.Build.0 = Release|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{303C29E5-A09D-39F2-AEF9-22B88E690FAC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {035C8AA4-0892-3D84-8B1A-0FE70260EB15}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
