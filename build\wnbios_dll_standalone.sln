﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}"
	ProjectSection(ProjectDependencies) = postProject
		{70CF8457-096E-3E77-B10E-19D5579B5516} = {70CF8457-096E-3E77-B10E-19D5579B5516}
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B} = {53FA39A2-B411-3B38-A0B7-98F05B58B46B}
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E} = {BB6EA5BF-B23A-301E-9973-B97171F2C14E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{A1C2F5C2-F2B9-3128-BDD0-47A2DD11FF26}"
	ProjectSection(ProjectDependencies) = postProject
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88} = {B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}
		{70CF8457-096E-3E77-B10E-19D5579B5516} = {70CF8457-096E-3E77-B10E-19D5579B5516}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{70CF8457-096E-3E77-B10E-19D5579B5516}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "c_example", "c_example.vcxproj", "{53FA39A2-B411-3B38-A0B7-98F05B58B46B}"
	ProjectSection(ProjectDependencies) = postProject
		{70CF8457-096E-3E77-B10E-19D5579B5516} = {70CF8457-096E-3E77-B10E-19D5579B5516}
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E} = {BB6EA5BF-B23A-301E-9973-B97171F2C14E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "wnbios_dll", "wnbios_dll.vcxproj", "{BB6EA5BF-B23A-301E-9973-B97171F2C14E}"
	ProjectSection(ProjectDependencies) = postProject
		{70CF8457-096E-3E77-B10E-19D5579B5516} = {70CF8457-096E-3E77-B10E-19D5579B5516}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.Debug|x64.ActiveCfg = Debug|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.Debug|x64.Build.0 = Debug|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.Release|x64.ActiveCfg = Release|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.Release|x64.Build.0 = Release|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B1CBB7C2-354D-3AD9-B38E-3ABE4F6C1D88}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A1C2F5C2-F2B9-3128-BDD0-47A2DD11FF26}.Debug|x64.ActiveCfg = Debug|x64
		{A1C2F5C2-F2B9-3128-BDD0-47A2DD11FF26}.Release|x64.ActiveCfg = Release|x64
		{A1C2F5C2-F2B9-3128-BDD0-47A2DD11FF26}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A1C2F5C2-F2B9-3128-BDD0-47A2DD11FF26}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.Debug|x64.ActiveCfg = Debug|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.Debug|x64.Build.0 = Debug|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.Release|x64.ActiveCfg = Release|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.Release|x64.Build.0 = Release|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{70CF8457-096E-3E77-B10E-19D5579B5516}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.Debug|x64.ActiveCfg = Debug|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.Debug|x64.Build.0 = Debug|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.Release|x64.ActiveCfg = Release|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.Release|x64.Build.0 = Release|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{53FA39A2-B411-3B38-A0B7-98F05B58B46B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.Debug|x64.ActiveCfg = Debug|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.Debug|x64.Build.0 = Debug|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.Release|x64.ActiveCfg = Release|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.Release|x64.Build.0 = Release|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BB6EA5BF-B23A-301E-9973-B97171F2C14E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CB5D4F72-9155-3328-82CE-2465B89EAF2B}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
