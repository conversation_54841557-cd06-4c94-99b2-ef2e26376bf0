#include <stdio.h>
#include <stdlib.h>
#include <windows.h>
#include "../include/wnbios_dll.h"

// 日志回调函数
void WNBIOS_CALL log_callback(WnbiosLogLevel level, const char* message) {
    const char* level_str;
    switch (level) {
        case WNBIOS_LOG_ERROR: level_str = "ERROR"; break;
        case WNBIOS_LOG_WARNING: level_str = "WARN"; break;
        case WNBIOS_LOG_INFO: level_str = "INFO"; break;
        case WNBIOS_LOG_DEBUG: level_str = "DEBUG"; break;
        default: level_str = "UNKNOWN"; break;
    }
    printf("[%s] %s\n", level_str, message);
}

int main() {
    printf("=== wnbios_dll C语言示例 ===\n\n");
    
    // 设置日志回调
    wnbios_set_log_callback(log_callback);
    wnbios_set_log_level(WNBIOS_LOG_INFO);
    
    // 获取版本信息
    int major, minor, patch;
    wnbios_get_version(&major, &minor, &patch);
    printf("DLL版本: %d.%d.%d\n", major, minor, patch);
    printf("支持的系统: %s\n\n", wnbios_get_supported_versions());
    
    // 初始化驱动
    printf("正在初始化驱动...\n");
    int result = wnbios_init_driver();
    if (result != WNBIOS_SUCCESS) {
        WnbiosErrorInfo error;
        wnbios_get_last_error(&error);
        printf("驱动初始化失败: %s (错误码: %d)\n", error.error_message, error.error_code);
        return 1;
    }
    printf("驱动初始化成功!\n\n");
    
    // 检查驱动状态
    if (wnbios_is_driver_loaded()) {
        printf("驱动已加载\n\n");
    }
    
    // 枚举进程
    printf("正在枚举进程...\n");
    WnbiosProcessInfo processes[100];
    int process_count = 100;
    
    result = wnbios_enum_processes(processes, &process_count);
    if (result == WNBIOS_SUCCESS) {
        printf("找到 %d 个进程:\n", process_count);
        for (int i = 0; i < min(10, process_count); i++) {  // 只显示前10个
            printf("  [%d] %s (PID: %d, Base: 0x%llx)\n", 
                   i + 1, processes[i].name, processes[i].pid, processes[i].base_address);
        }
        if (process_count > 10) {
            printf("  ... 还有 %d 个进程\n", process_count - 10);
        }
    } else {
        WnbiosErrorInfo error;
        wnbios_get_last_error(&error);
        printf("进程枚举失败: %s\n", error.error_message);
    }
    printf("\n");
    
    // 查找特定进程
    printf("正在查找 notepad.exe 进程...\n");
    WnbiosProcessInfo notepad_process;
    result = wnbios_find_process_by_name("notepad.exe", &notepad_process);
    if (result == WNBIOS_SUCCESS) {
        printf("找到 notepad.exe:\n");
        printf("  PID: %d\n", notepad_process.pid);
        printf("  基址: 0x%llx\n", notepad_process.base_address);
        printf("  CR3: 0x%llx\n", notepad_process.cr3);
        
        // 设置为目标进程
        result = wnbios_set_target_process("notepad.exe");
        if (result == WNBIOS_SUCCESS) {
            printf("  已设置为目标进程\n");
            
            // 尝试读取内存
            printf("  正在读取进程内存...\n");
            unsigned char buffer[16];
            int bytes_read = wnbios_read_memory(notepad_process.base_address, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                printf("  读取了 %d 字节: ", bytes_read);
                for (int i = 0; i < bytes_read; i++) {
                    printf("%02X ", buffer[i]);
                }
                printf("\n");
                
                // 检查PE头
                if (bytes_read >= 2 && buffer[0] == 0x4D && buffer[1] == 0x5A) {
                    printf("  检测到有效的PE文件头 (MZ)\n");
                }
            } else {
                WnbiosErrorInfo error;
                wnbios_get_last_error(&error);
                printf("  内存读取失败: %s\n", error.error_message);
            }
            
            // 枚举模块
            printf("  正在枚举模块...\n");
            WnbiosModuleInfo modules[50];
            int module_count = 50;
            result = wnbios_enum_modules("notepad.exe", modules, &module_count);
            if (result == WNBIOS_SUCCESS) {
                printf("  找到 %d 个模块:\n", module_count);
                for (int i = 0; i < min(5, module_count); i++) {
                    wprintf(L"    [%d] %s (Base: 0x%llx, Size: 0x%x)\n", 
                           i + 1, modules[i].name, modules[i].base_address, modules[i].size);
                }
                if (module_count > 5) {
                    printf("    ... 还有 %d 个模块\n", module_count - 5);
                }
            } else {
                WnbiosErrorInfo error;
                wnbios_get_last_error(&error);
                printf("  模块枚举失败: %s\n", error.error_message);
            }
        }
    } else {
        printf("未找到 notepad.exe 进程\n");
        printf("提示: 请先启动记事本程序\n");
    }
    printf("\n");
    
    // 清理
    printf("正在清理驱动...\n");
    wnbios_cleanup_driver();
    printf("清理完成\n");
    
    printf("\n按任意键退出...\n");
    getchar();
    
    return 0;
}