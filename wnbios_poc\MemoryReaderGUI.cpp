#include "MemoryReaderGUI.h"
#include <iostream>
#include <vector>
#include <string>
#include <windows.h>
#include <tlhelp32.h>
#include <vcclr.h>

using namespace msclr::interop;

void MemoryReaderForm::InitializeComponent(void)
{
    this->groupBoxDriver = (gcnew System::Windows::Forms::GroupBox());
    this->btnInitDriver = (gcnew System::Windows::Forms::Button());
    this->lblDriverStatus = (gcnew System::Windows::Forms::Label());

    this->groupBoxProcess = (gcnew System::Windows::Forms::GroupBox());
    this->comboBoxProcesses = (gcnew System::Windows::Forms::ComboBox());
    this->btnRefreshProcesses = (gcnew System::Windows::Forms::Button());
    this->lblProcessInfo = (gcnew System::Windows::Forms::Label());
    this->txtProcessFilter = (gcnew System::Windows::Forms::TextBox());
    this->lblProcessFilter = (gcnew System::Windows::Forms::Label());

    this->groupBoxModules = (gcnew System::Windows::Forms::GroupBox());
    this->listBoxModules = (gcnew System::Windows::Forms::ListBox());
    this->btnRefreshModules = (gcnew System::Windows::Forms::Button());
    this->lblModuleInfo = (gcnew System::Windows::Forms::Label());

    this->groupBoxMemory = (gcnew System::Windows::Forms::GroupBox());
    this->txtAddress = (gcnew System::Windows::Forms::TextBox());
    this->numSize = (gcnew System::Windows::Forms::NumericUpDown());
    this->btnReadMemory = (gcnew System::Windows::Forms::Button());
    this->lblAddress = (gcnew System::Windows::Forms::Label());
    this->lblSize = (gcnew System::Windows::Forms::Label());
    this->lblSingleReadCount = (gcnew System::Windows::Forms::Label());
    this->numSingleReadCount = (gcnew System::Windows::Forms::NumericUpDown());
    this->btnContinuousRead = (gcnew System::Windows::Forms::Button());
    this->timerSingleRead = (gcnew System::Windows::Forms::Timer());

    this->groupBoxOutput = (gcnew System::Windows::Forms::GroupBox());
    this->comboBoxDataType = (gcnew System::Windows::Forms::ComboBox());
    this->txtOutput = (gcnew System::Windows::Forms::TextBox());
    this->btnCopyOutput = (gcnew System::Windows::Forms::Button());

    this->groupBoxBatchRead = (gcnew System::Windows::Forms::GroupBox());
    this->txtBatchAddresses = (gcnew System::Windows::Forms::TextBox());
    this->btnBatchRead = (gcnew System::Windows::Forms::Button());
    this->btnClearBatch = (gcnew System::Windows::Forms::Button());
    this->lblBatchInstructions = (gcnew System::Windows::Forms::Label());
    this->numReadCount = (gcnew System::Windows::Forms::NumericUpDown());
    this->lblReadCount = (gcnew System::Windows::Forms::Label());
    this->chkContinuousRead = (gcnew System::Windows::Forms::CheckBox());
    this->timerContinuousRead = (gcnew System::Windows::Forms::Timer());

    this->statusStrip = (gcnew System::Windows::Forms::StatusStrip());
    this->statusLabel = (gcnew System::Windows::Forms::ToolStripStatusLabel());

    this->SuspendLayout();

    // Form
    this->AutoScaleDimensions = System::Drawing::SizeF(6, 13);
    this->AutoScaleMode = System::Windows::Forms::AutoScaleMode::Font;
    this->ClientSize = System::Drawing::Size(1200, 800);
    this->Text = L"内存读取器 - wnbios_poc";
    this->StartPosition = FormStartPosition::CenterScreen;
    this->MinimumSize = System::Drawing::Size(1000, 600);
    this->FormBorderStyle = System::Windows::Forms::FormBorderStyle::Sizable;
    this->MaximizeBox = true;
    this->MinimizeBox = true;
    this->Resize += gcnew System::EventHandler(this, &MemoryReaderForm::Form_Resize);

    // Driver GroupBox - 横跨整个顶部
    this->groupBoxDriver->Location = System::Drawing::Point(12, 12);
    this->groupBoxDriver->Size = System::Drawing::Size(1160, 80);
    this->groupBoxDriver->Text = L"驱动控制";
    this->groupBoxDriver->TabIndex = 0;
    this->groupBoxDriver->Anchor = AnchorStyles::Top | AnchorStyles::Left | AnchorStyles::Right;

    this->btnInitDriver->Location = System::Drawing::Point(15, 25);
    this->btnInitDriver->Size = System::Drawing::Size(120, 30);
    this->btnInitDriver->Text = L"初始化驱动";
    this->btnInitDriver->UseVisualStyleBackColor = true;
    this->btnInitDriver->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnInitDriver_Click);

    this->lblDriverStatus->Location = System::Drawing::Point(150, 25);
    this->lblDriverStatus->Size = System::Drawing::Size(1000, 30);
    this->lblDriverStatus->Text = L"驱动未初始化";
    this->lblDriverStatus->ForeColor = System::Drawing::Color::Red;
    this->lblDriverStatus->TextAlign = ContentAlignment::MiddleLeft;
    this->lblDriverStatus->Anchor = AnchorStyles::Top | AnchorStyles::Left | AnchorStyles::Right;

    this->groupBoxDriver->Controls->Add(this->btnInitDriver);
    this->groupBoxDriver->Controls->Add(this->lblDriverStatus);

    // Process GroupBox - 左半部分
    this->groupBoxProcess->Location = System::Drawing::Point(12, 100);
    this->groupBoxProcess->Size = System::Drawing::Size(570, 180);
    this->groupBoxProcess->Text = L"进程选择";
    this->groupBoxProcess->TabIndex = 1;
    this->groupBoxProcess->Anchor = AnchorStyles::Top | AnchorStyles::Left;

    this->lblProcessFilter->Location = System::Drawing::Point(15, 25);
    this->lblProcessFilter->Size = System::Drawing::Size(50, 20);
    this->lblProcessFilter->Text = L"过滤:";

    this->txtProcessFilter->Location = System::Drawing::Point(70, 23);
    this->txtProcessFilter->Size = System::Drawing::Size(280, 25);
    this->txtProcessFilter->TextChanged += gcnew System::EventHandler(this, &MemoryReaderForm::txtProcessFilter_TextChanged);

    this->comboBoxProcesses->Location = System::Drawing::Point(15, 55);
    this->comboBoxProcesses->Size = System::Drawing::Size(360, 25);
    this->comboBoxProcesses->DropDownStyle = ComboBoxStyle::DropDownList;
    this->comboBoxProcesses->SelectedIndexChanged += gcnew System::EventHandler(this, &MemoryReaderForm::comboBoxProcesses_SelectedIndexChanged);

    this->btnRefreshProcesses->Location = System::Drawing::Point(385, 53);
    this->btnRefreshProcesses->Size = System::Drawing::Size(90, 30);
    this->btnRefreshProcesses->Text = L"刷新";
    this->btnRefreshProcesses->UseVisualStyleBackColor = true;
    this->btnRefreshProcesses->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnRefreshProcesses_Click);

    this->lblProcessInfo->Location = System::Drawing::Point(15, 90);
    this->lblProcessInfo->Size = System::Drawing::Size(540, 80);
    this->lblProcessInfo->Text = L"未选择进程";

    this->groupBoxProcess->Controls->Add(this->lblProcessFilter);
    this->groupBoxProcess->Controls->Add(this->txtProcessFilter);
    this->groupBoxProcess->Controls->Add(this->comboBoxProcesses);
    this->groupBoxProcess->Controls->Add(this->btnRefreshProcesses);
    this->groupBoxProcess->Controls->Add(this->lblProcessInfo);

    // Batch Read GroupBox - 右半部分
    this->groupBoxBatchRead->Location = System::Drawing::Point(600, 100);
    this->groupBoxBatchRead->Size = System::Drawing::Size(570, 180);
    this->groupBoxBatchRead->Text = L"批量地址读取";
    this->groupBoxBatchRead->TabIndex = 5;
    this->groupBoxBatchRead->Anchor = AnchorStyles::Top | AnchorStyles::Right;

    this->lblBatchInstructions->Location = System::Drawing::Point(15, 25);
    this->lblBatchInstructions->Size = System::Drawing::Size(540, 20);
    this->lblBatchInstructions->Text = L"每行输入一个地址 (格式: 0x1234567890ABCDEF)";
    this->lblBatchInstructions->ForeColor = System::Drawing::Color::Gray;

    this->txtBatchAddresses->Location = System::Drawing::Point(15, 50);
    this->txtBatchAddresses->Size = System::Drawing::Size(540, 60);
    this->txtBatchAddresses->Multiline = true;
    this->txtBatchAddresses->ScrollBars = ScrollBars::Vertical;
    this->txtBatchAddresses->Font = gcnew System::Drawing::Font("Consolas", 9);

    // 读取次数控件
    this->lblReadCount->Location = System::Drawing::Point(15, 115);
    this->lblReadCount->Size = System::Drawing::Size(80, 20);
    this->lblReadCount->Text = L"读取次数:";

    this->numReadCount->Location = System::Drawing::Point(100, 113);
    this->numReadCount->Size = System::Drawing::Size(80, 25);
    this->numReadCount->Minimum = 0;
    this->numReadCount->Maximum = 99999;
    this->numReadCount->Value = 1;

    // 连续读取复选框
    this->chkContinuousRead->Location = System::Drawing::Point(190, 115);
    this->chkContinuousRead->Size = System::Drawing::Size(150, 20);
    this->chkContinuousRead->Text = L"连续读取 (0=无限)";
    this->chkContinuousRead->CheckedChanged += gcnew System::EventHandler(this, &MemoryReaderForm::chkContinuousRead_CheckedChanged);

    // 定时器设置
    this->timerContinuousRead->Interval = 1000; // 1秒间隔
    this->timerContinuousRead->Tick += gcnew System::EventHandler(this, &MemoryReaderForm::timerContinuousRead_Tick);

    this->btnBatchRead->Location = System::Drawing::Point(15, 145);
    this->btnBatchRead->Size = System::Drawing::Size(100, 30);
    this->btnBatchRead->Text = L"批量读取";
    this->btnBatchRead->UseVisualStyleBackColor = true;
    this->btnBatchRead->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnBatchRead_Click);

    this->btnClearBatch->Location = System::Drawing::Point(125, 145);
    this->btnClearBatch->Size = System::Drawing::Size(80, 30);
    this->btnClearBatch->Text = L"清空";
    this->btnClearBatch->UseVisualStyleBackColor = true;
    this->btnClearBatch->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnClearBatch_Click);

    this->groupBoxBatchRead->Controls->Add(this->lblBatchInstructions);
    this->groupBoxBatchRead->Controls->Add(this->txtBatchAddresses);
    this->groupBoxBatchRead->Controls->Add(this->lblReadCount);
    this->groupBoxBatchRead->Controls->Add(this->numReadCount);
    this->groupBoxBatchRead->Controls->Add(this->chkContinuousRead);
    this->groupBoxBatchRead->Controls->Add(this->btnBatchRead);
    this->groupBoxBatchRead->Controls->Add(this->btnClearBatch);

    // Modules GroupBox - 横跨整个宽度
    this->groupBoxModules->Location = System::Drawing::Point(12, 290);
    this->groupBoxModules->Size = System::Drawing::Size(1160, 150);
    this->groupBoxModules->Text = L"模块列表";
    this->groupBoxModules->TabIndex = 2;
    this->groupBoxModules->Anchor = AnchorStyles::Top | AnchorStyles::Left | AnchorStyles::Right;

    this->listBoxModules->Location = System::Drawing::Point(15, 25);
    this->listBoxModules->Size = System::Drawing::Size(1050, 110);
    this->listBoxModules->Enabled = false;
    this->listBoxModules->SelectedIndexChanged += gcnew System::EventHandler(this, &MemoryReaderForm::listBoxModules_SelectedIndexChanged);
    this->listBoxModules->Anchor = AnchorStyles::Top | AnchorStyles::Left | AnchorStyles::Right;

    this->btnRefreshModules->Location = System::Drawing::Point(1070, 25);
    this->btnRefreshModules->Size = System::Drawing::Size(80, 30);
    this->btnRefreshModules->Text = L"刷新";
    this->btnRefreshModules->Enabled = false;
    this->btnRefreshModules->UseVisualStyleBackColor = true;
    this->btnRefreshModules->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnRefreshModules_Click);
    this->btnRefreshModules->Anchor = AnchorStyles::Top | AnchorStyles::Right;

    this->lblModuleInfo->Location = System::Drawing::Point(15, 170);
    this->lblModuleInfo->Size = System::Drawing::Size(550, 25);
    this->lblModuleInfo->Text = L"未选择模块";
    this->lblModuleInfo->Anchor = AnchorStyles::Top | AnchorStyles::Left | AnchorStyles::Right;

    this->groupBoxModules->Controls->Add(this->listBoxModules);
    this->groupBoxModules->Controls->Add(this->btnRefreshModules);
    this->groupBoxModules->Controls->Add(this->lblModuleInfo);

    // Memory GroupBox - 单个地址读取
    this->groupBoxMemory->Location = System::Drawing::Point(12, 450);
    this->groupBoxMemory->Size = System::Drawing::Size(570, 150);
    this->groupBoxMemory->Text = L"单个地址读取";
    this->groupBoxMemory->TabIndex = 3;
    this->groupBoxMemory->Anchor = AnchorStyles::Top | AnchorStyles::Left;

    this->lblAddress->Location = System::Drawing::Point(15, 25);
    this->lblAddress->Size = System::Drawing::Size(60, 20);
    this->lblAddress->Text = L"地址:";

    this->txtAddress->Location = System::Drawing::Point(80, 23);
    this->txtAddress->Size = System::Drawing::Size(200, 25);
    this->txtAddress->Text = L"0x";
    this->txtAddress->Anchor = AnchorStyles::Top | AnchorStyles::Left;

    this->lblSize->Location = System::Drawing::Point(300, 25);
    this->lblSize->Size = System::Drawing::Size(50, 20);
    this->lblSize->Text = L"大小:";

    this->numSize->Location = System::Drawing::Point(355, 23);
    this->numSize->Size = System::Drawing::Size(75, 25);
    this->numSize->Minimum = 1;
    this->numSize->Maximum = 1024;
    this->numSize->Value = 8;

    this->btnReadMemory->Location = System::Drawing::Point(450, 20);
    this->btnReadMemory->Size = System::Drawing::Size(100, 30);
    this->btnReadMemory->Text = L"单次读取";
    this->btnReadMemory->UseVisualStyleBackColor = true;
    this->btnReadMemory->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnReadMemory_Click);
    this->btnReadMemory->Anchor = AnchorStyles::Top | AnchorStyles::Right;

    // 第二行：连续读取控件
    this->lblSingleReadCount->Location = System::Drawing::Point(15, 60);
    this->lblSingleReadCount->Size = System::Drawing::Size(80, 20);
    this->lblSingleReadCount->Text = L"读取次数:";

    this->numSingleReadCount->Location = System::Drawing::Point(100, 58);
    this->numSingleReadCount->Size = System::Drawing::Size(80, 25);
    this->numSingleReadCount->Minimum = 0;
    this->numSingleReadCount->Maximum = 99999;
    this->numSingleReadCount->Value = 1;

    this->btnContinuousRead->Location = System::Drawing::Point(200, 55);
    this->btnContinuousRead->Size = System::Drawing::Size(120, 30);
    this->btnContinuousRead->Text = L"连续读取";
    this->btnContinuousRead->UseVisualStyleBackColor = true;
    this->btnContinuousRead->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnContinuousRead_Click);

    // 定时器设置
    this->timerSingleRead->Interval = 1000; // 1秒间隔
    this->timerSingleRead->Tick += gcnew System::EventHandler(this, &MemoryReaderForm::timerSingleRead_Tick);

    this->groupBoxMemory->Controls->Add(this->lblAddress);
    this->groupBoxMemory->Controls->Add(this->txtAddress);
    this->groupBoxMemory->Controls->Add(this->lblSize);
    this->groupBoxMemory->Controls->Add(this->numSize);
    this->groupBoxMemory->Controls->Add(this->btnReadMemory);
    this->groupBoxMemory->Controls->Add(this->lblSingleReadCount);
    this->groupBoxMemory->Controls->Add(this->numSingleReadCount);
    this->groupBoxMemory->Controls->Add(this->btnContinuousRead);

    // Output GroupBox - 横跨整个宽度
    this->groupBoxOutput->Location = System::Drawing::Point(12, 610);
    this->groupBoxOutput->Size = System::Drawing::Size(1160, 140);
    this->groupBoxOutput->Text = L"输出结果";
    this->groupBoxOutput->TabIndex = 4;
    this->groupBoxOutput->Anchor = AnchorStyles::Top | AnchorStyles::Bottom | AnchorStyles::Left | AnchorStyles::Right;

    this->comboBoxDataType->Location = System::Drawing::Point(15, 25);
    this->comboBoxDataType->Size = System::Drawing::Size(120, 25);
    this->comboBoxDataType->DropDownStyle = ComboBoxStyle::DropDownList;
    this->comboBoxDataType->Items->Add("十六进制");
    this->comboBoxDataType->Items->Add("ASCII");
    this->comboBoxDataType->Items->Add("Unicode");
    this->comboBoxDataType->Items->Add("指针");
    this->comboBoxDataType->Items->Add("Int8");
    this->comboBoxDataType->Items->Add("Int16");
    this->comboBoxDataType->Items->Add("Int32");
    this->comboBoxDataType->Items->Add("Int64");
    this->comboBoxDataType->Items->Add("UInt8");
    this->comboBoxDataType->Items->Add("UInt16");
    this->comboBoxDataType->Items->Add("UInt32");
    this->comboBoxDataType->Items->Add("UInt64");
    this->comboBoxDataType->Items->Add("Float");
    this->comboBoxDataType->Items->Add("Double");
    this->comboBoxDataType->SelectedIndex = 0;

    this->btnCopyOutput->Location = System::Drawing::Point(150, 25);
    this->btnCopyOutput->Size = System::Drawing::Size(80, 25);
    this->btnCopyOutput->Text = L"复制";
    this->btnCopyOutput->UseVisualStyleBackColor = true;
    this->btnCopyOutput->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnCopyOutput_Click);

    this->txtOutput->Location = System::Drawing::Point(15, 60);
    this->txtOutput->Size = System::Drawing::Size(1130, 70);
    this->txtOutput->Multiline = true;
    this->txtOutput->ScrollBars = ScrollBars::Both;
    this->txtOutput->ReadOnly = true;
    this->txtOutput->Font = gcnew System::Drawing::Font("Consolas", 9);
    this->txtOutput->Anchor = AnchorStyles::Top | AnchorStyles::Bottom | AnchorStyles::Left | AnchorStyles::Right;

    this->groupBoxOutput->Controls->Add(this->comboBoxDataType);
    this->groupBoxOutput->Controls->Add(this->btnCopyOutput);
    this->groupBoxOutput->Controls->Add(this->txtOutput);

    // Status Strip
    this->statusStrip->Dock = DockStyle::Bottom;
    this->statusLabel->Text = L"就绪";
    this->statusStrip->Items->Add(this->statusLabel);

    // Add all controls to form
    this->Controls->Add(this->groupBoxDriver);
    this->Controls->Add(this->groupBoxProcess);
    this->Controls->Add(this->groupBoxBatchRead);
    this->Controls->Add(this->groupBoxModules);
    this->Controls->Add(this->groupBoxMemory);
    this->Controls->Add(this->groupBoxOutput);
    this->Controls->Add(this->statusStrip);

    this->ResumeLayout(false);
    this->PerformLayout();
}

// Event Handlers
System::Void MemoryReaderForm::btnInitDriver_Click(System::Object^ sender, System::EventArgs^ e)
{
    try
    {
        if (driver != nullptr)
        {
            delete driver;
            driver = nullptr;
        }

        SetStatus("正在初始化驱动...");
        driver = new eneio_lib();

        UpdateDriverStatus(true);
        btnRefreshProcesses->Enabled = true;
        SetStatus("驱动初始化成功");
    }
    catch (const std::exception& ex)
    {
        UpdateDriverStatus(false);
        String^ errorMsg = gcnew String(ex.what());
        MessageBox::Show("驱动初始化失败: " + errorMsg, "错误",
                        MessageBoxButtons::OK, MessageBoxIcon::Error);
        SetStatus("驱动初始化失败");
    }
}

System::Void MemoryReaderForm::btnRefreshProcesses_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("请先初始化驱动", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    RefreshProcessList();
}

System::Void MemoryReaderForm::btnRefreshModules_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("Please initialize the driver first", "Warning",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    RefreshModuleList();
}

System::Void MemoryReaderForm::btnReadMemory_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("请先初始化驱动", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    ProcessInfo^ selectedProcess = dynamic_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    if (selectedProcess == nullptr)
    {
        MessageBox::Show("请先选择一个进程", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    String^ addressText = txtAddress->Text->Trim();
    if (String::IsNullOrEmpty(addressText) || !addressText->StartsWith("0x"))
    {
        MessageBox::Show("请输入有效的地址 (格式: 0x1234567890ABCDEF)", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    // 清空上次读取的数据，进行单次读取
    lastSingleReadData = nullptr;
    PerformSingleRead();
}

System::Void MemoryReaderForm::btnCopyOutput_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (!String::IsNullOrEmpty(txtOutput->Text))
    {
        Clipboard::SetText(txtOutput->Text);
        SetStatus("Output copied to clipboard");
    }
}

System::Void MemoryReaderForm::comboBoxProcesses_SelectedIndexChanged(System::Object^ sender, System::EventArgs^ e)
{
    if (comboBoxProcesses->SelectedItem != nullptr)
    {
        ProcessInfo^ procInfo = safe_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
        lblProcessInfo->Text = String::Format("PID: {0}\nBase: 0x{1:X}",
                                            procInfo->PID, procInfo->BaseAddress);

        // Enable module refresh
        btnRefreshModules->Enabled = true;
        listBoxModules->Enabled = true;

        // Clear previous modules
        listBoxModules->Items->Clear();

        SetStatus("Process selected: " + procInfo->Name);
    }
}

System::Void MemoryReaderForm::listBoxModules_SelectedIndexChanged(System::Object^ sender, System::EventArgs^ e)
{
    if (listBoxModules->SelectedItem != nullptr)
    {
        ModuleInfo^ modInfo = safe_cast<ModuleInfo^>(listBoxModules->SelectedItem);
        txtAddress->Text = String::Format("0x{0:X}", modInfo->BaseAddress);

        SetStatus("Module selected: " + modInfo->Name);
    }
}

System::Void MemoryReaderForm::txtProcessFilter_TextChanged(System::Object^ sender, System::EventArgs^ e)
{
    ApplyProcessFilter();
}

void MemoryReaderForm::UpdateDriverStatus(bool initialized)
{
    if (initialized)
    {
        lblDriverStatus->Text = L"驱动已成功初始化";
        lblDriverStatus->ForeColor = System::Drawing::Color::Green;
        btnInitDriver->Text = L"重新初始化驱动";
    }
    else
    {
        lblDriverStatus->Text = L"驱动未初始化";
        lblDriverStatus->ForeColor = System::Drawing::Color::Red;
        btnInitDriver->Text = L"初始化驱动";
        btnRefreshProcesses->Enabled = false;
        btnRefreshModules->Enabled = false;
        listBoxModules->Enabled = false;
        comboBoxProcesses->Items->Clear();
        listBoxModules->Items->Clear();
    }
}

void MemoryReaderForm::RefreshProcessList()
{
    SetStatus("Refreshing process list...");
    allProcesses->Clear();
    comboBoxProcesses->Items->Clear();

    try
    {
        // Get list of running processes using Windows API
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
            return;

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32))
        {
            do
            {
                // Convert process name to managed string
                String^ processName = gcnew String(pe32.szExeFile);

                // Try to get process base address using our driver
                pin_ptr<const wchar_t> wch = PtrToStringChars(processName);
                std::wstring wstr(wch);
                std::string stdProcessName(wstr.begin(), wstr.end());
                uintptr_t baseAddr = driver->get_process_base(stdProcessName.c_str());

                if (baseAddr != 0)
                {
                    ProcessInfo^ procInfo = gcnew ProcessInfo(processName, pe32.th32ProcessID, baseAddr);
                    allProcesses->Add(procInfo);
                }

            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);

        // Apply current filter
        ApplyProcessFilter();

        SetStatus(String::Format("找到 {0} 个可访问进程", allProcesses->Count));
    }
    catch (...)
    {
        SetStatus("Error refreshing process list");
    }
}

void MemoryReaderForm::RefreshModuleList()
{
    if (comboBoxProcesses->SelectedItem == nullptr)
    {
        MessageBox::Show("Please select a process first", "Warning",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    ProcessInfo^ procInfo = safe_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    pin_ptr<const wchar_t> wch = PtrToStringChars(procInfo->Name);
    std::wstring wstr(wch);
    std::string processName(wstr.begin(), wstr.end());

    SetStatus("Refreshing module list...");
    listBoxModules->Items->Clear();

    try
    {
        SetStatus(String::Format("Getting PEB for process: {0} (PID: {1})", procInfo->Name, procInfo->PID));

        // Get PEB address
        uintptr_t pebAddr = driver->get_process_peb(processName.c_str());
        if (pebAddr == 0)
        {
            SetStatus(String::Format("Failed to get PEB address for {0}. Try using console mode for debugging.", procInfo->Name));

            // Try alternative: use the driver's built-in module enumeration for debugging
            pin_ptr<const wchar_t> wch = PtrToStringChars(procInfo->Name);
            std::wstring wstr(wch);
            std::string stdProcessName(wstr.begin(), wstr.end());

            // This will print debug info to console
            driver->enumerate_process_modules(stdProcessName.c_str());

            return;
        }

        SetStatus(String::Format("PEB address: 0x{0:X}", pebAddr));

        // Enumerate modules from PEB
        EnumerateModulesFromPEB(pebAddr, procInfo->Name);

        SetStatus(String::Format("找到 {0} 个模块", listBoxModules->Items->Count));
    }
    catch (...)
    {
        SetStatus("Error refreshing module list");
    }
}

void MemoryReaderForm::ReadAndDisplayMemory()
{
    try
    {
        String^ addressStr = txtAddress->Text->Trim();
        if (String::IsNullOrEmpty(addressStr))
        {
            MessageBox::Show("Please enter a memory address", "Warning",
                            MessageBoxButtons::OK, MessageBoxIcon::Warning);
            return;
        }

        // Parse address (support both 0x prefix and plain hex)
        uintptr_t address;
        if (addressStr->StartsWith("0x") || addressStr->StartsWith("0X"))
        {
            addressStr = addressStr->Substring(2);
        }

        pin_ptr<const wchar_t> wch = PtrToStringChars(addressStr);
        std::wstring wstr(wch);
        std::string stdAddressStr(wstr.begin(), wstr.end());

        address = std::stoull(stdAddressStr, nullptr, 16);

        int size = (int)numSize->Value;

        // Read memory
        std::vector<uint8_t> buffer(size);
        bool success = driver->read_virtual_memory(address, buffer.data(), size);

        if (!success)
        {
            txtOutput->Text = "Failed to read memory at address 0x" + address.ToString("X");
            SetStatus("Memory read failed");
            return;
        }

        // Convert to managed array
        array<System::Byte>^ managedBuffer = gcnew array<System::Byte>(size);
        for (int i = 0; i < size; i++)
        {
            managedBuffer[i] = buffer[i];
        }

        // Format output based on selected data type
        String^ dataType = comboBoxDataType->SelectedItem->ToString();
        String^ output = FormatDataAsType(managedBuffer, dataType);

        txtOutput->Text = output;
        SetStatus(String::Format("Successfully read {0} bytes from 0x{1:X}", size, address));
    }
    catch (const std::exception& ex)
    {
        String^ errorMsg = gcnew String(ex.what());
        txtOutput->Text = "Error: " + errorMsg;
        SetStatus("Memory read error");
    }
    catch (...)
    {
        txtOutput->Text = "Unknown error occurred while reading memory";
        SetStatus("Memory read error");
    }
}

String^ MemoryReaderForm::FormatDataAsType(array<System::Byte>^ data, String^ dataType)
{
    if (data->Length == 0)
        return "No data";

    System::Text::StringBuilder^ sb = gcnew System::Text::StringBuilder();

    if (dataType == "Hex")
    {
        return ByteArrayToHexString(data);
    }
    else if (dataType == "ASCII")
    {
        for (int i = 0; i < data->Length; i++)
        {
            char c = (char)data[i];
            if (c >= 32 && c <= 126)
                sb->Append(c);
            else
                sb->Append('.');
        }
        return sb->ToString();
    }
    else if (dataType == "Unicode")
    {
        if (data->Length >= 2)
        {
            for (int i = 0; i < data->Length - 1; i += 2)
            {
                wchar_t wc = (wchar_t)(data[i] | (data[i + 1] << 8));
                if (wc >= 32 && wc <= 126)
                    sb->Append((char)wc);
                else
                    sb->Append('.');
            }
        }
        return sb->ToString();
    }
    else if (dataType == "Int8" && data->Length >= 1)
    {
        return ((System::SByte)data[0]).ToString();
    }
    else if (dataType == "Int16" && data->Length >= 2)
    {
        short value = (short)(data[0] | (data[1] << 8));
        return value.ToString();
    }
    else if (dataType == "Int32" && data->Length >= 4)
    {
        int value = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);
        return value.ToString();
    }
    else if (dataType == "Int64" && data->Length >= 8)
    {
        long long value = 0;
        for (int i = 0; i < 8; i++)
        {
            value |= ((long long)data[i]) << (i * 8);
        }
        return value.ToString();
    }
    else if (dataType == "UInt8" && data->Length >= 1)
    {
        return data[0].ToString();
    }
    else if (dataType == "UInt16" && data->Length >= 2)
    {
        unsigned short value = (unsigned short)(data[0] | (data[1] << 8));
        return value.ToString();
    }
    else if (dataType == "UInt32" && data->Length >= 4)
    {
        unsigned int value = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);
        return value.ToString();
    }
    else if (dataType == "UInt64" && data->Length >= 8)
    {
        unsigned long long value = 0;
        for (int i = 0; i < 8; i++)
        {
            value |= ((unsigned long long)data[i]) << (i * 8);
        }
        return value.ToString();
    }
    else if (dataType == "Float" && data->Length >= 4)
    {
        pin_ptr<System::Byte> pinnedData = &data[0];
        float value = *((float*)pinnedData);
        return value.ToString("F6");
    }
    else if (dataType == "Double" && data->Length >= 8)
    {
        pin_ptr<System::Byte> pinnedData = &data[0];
        double value = *((double*)pinnedData);
        return value.ToString("F6");
    }

    return "Insufficient data for selected type";
}

String^ MemoryReaderForm::ByteArrayToHexString(array<System::Byte>^ data)
{
    System::Text::StringBuilder^ sb = gcnew System::Text::StringBuilder();

    for (int i = 0; i < data->Length; i++)
    {
        if (i > 0 && i % 16 == 0)
            sb->AppendLine();
        else if (i > 0 && i % 8 == 0)
            sb->Append("  ");
        else if (i > 0)
            sb->Append(" ");

        sb->AppendFormat("{0:X2}", data[i]);
    }

    return sb->ToString();
}

void MemoryReaderForm::SetStatus(String^ message)
{
    statusLabel->Text = message;
    statusStrip->Refresh();
}

void MemoryReaderForm::EnumerateModulesFromPEB(uintptr_t pebAddr, String^ processName)
{
    try
    {
        SetStatus("Reading PEB structure...");

        // PEB + 0x18 = PEB_LDR_DATA
        uintptr_t ldrDataPtr = 0;
        if (!driver->read_virtual_memory(pebAddr + 0x18, &ldrDataPtr, sizeof(ldrDataPtr)))
        {
            SetStatus("Failed to read LDR data pointer");
            return;
        }

        if (ldrDataPtr == 0)
        {
            SetStatus("LDR data pointer is null");
            return;
        }

        // PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
        uintptr_t moduleList = 0;
        if (!driver->read_virtual_memory(ldrDataPtr + 0x20, &moduleList, sizeof(moduleList)))
        {
            SetStatus("Failed to read module list");
            return;
        }

        if (moduleList == 0)
        {
            SetStatus("Module list is null");
            return;
        }

        uintptr_t currentEntry = moduleList;
        int moduleCount = 0;
        const int maxModules = 10; // Limit to first 10 modules like the original code

        for (int i = 0; i < maxModules && currentEntry != 0; i++)
        {
            // Validate current entry address
            if (currentEntry < 0x1000)
            {
                SetStatus(String::Format("Invalid module entry address: 0x{0:X}", currentEntry));
                break;
            }

            // From list entry, subtract offset to get actual LDR_DATA_TABLE_ENTRY
            uintptr_t ldrEntry = currentEntry - 0x10;

            // DllBase at offset 0x30
            uintptr_t dllBase = 0;
            if (!driver->read_virtual_memory(ldrEntry + 0x30, &dllBase, sizeof(dllBase)))
            {
                SetStatus(String::Format("Failed to read DllBase at entry {0}", i));
                break;
            }

            // SizeOfImage at offset 0x40
            uintptr_t sizeOfImage = 0;
            if (!driver->read_virtual_memory(ldrEntry + 0x40, &sizeOfImage, sizeof(sizeOfImage)))
            {
                SetStatus(String::Format("Failed to read SizeOfImage at entry {0}", i));
                break;
            }

            // BaseDllName at offset 0x58 (UNICODE_STRING)
            UNICODE_STRING_NATIVE unicodeString;

            std::vector<wchar_t> moduleName(256, 0);
            if (driver->read_virtual_memory(ldrEntry + 0x58, &unicodeString, sizeof(unicodeString)))
            {
                if (unicodeString.Buffer && unicodeString.Length > 0 && unicodeString.Length < 512)
                {
                    size_t readSize = min(unicodeString.Length, (uint16_t)(moduleName.size() * sizeof(wchar_t) - 2));
                    if (driver->read_virtual_memory(unicodeString.Buffer, moduleName.data(), readSize))
                    {
                        String^ moduleNameStr = gcnew String(moduleName.data());
                        if (!String::IsNullOrEmpty(moduleNameStr))
                        {
                            ModuleInfo^ modInfo = gcnew ModuleInfo(moduleNameStr, dllBase, (uint32_t)sizeOfImage);
                            listBoxModules->Items->Add(modInfo);
                            moduleCount++;
                        }
                    }
                }
            }

            // Get next entry
            uintptr_t nextEntry = 0;
            if (!driver->read_virtual_memory(currentEntry, &nextEntry, sizeof(nextEntry)))
            {
                SetStatus(String::Format("Failed to read next entry at module {0}", i));
                break;
            }

            currentEntry = nextEntry;
        }

        if (moduleCount == 0)
        {
            SetStatus("未找到模块");
        }
        else
        {
            SetStatus(String::Format("Successfully enumerated {0} modules", moduleCount));
        }
    }
    catch (const std::exception& ex)
    {
        String^ errorMsg = gcnew String(ex.what());
        SetStatus("Exception during module enumeration: " + errorMsg);
    }
    catch (...)
    {
        SetStatus("Unknown error during module enumeration");
    }
}

void MemoryReaderForm::ApplyProcessFilter()
{
    String^ filterText = txtProcessFilter->Text->Trim()->ToLower();
    comboBoxProcesses->Items->Clear();

    for each (ProcessInfo^ procInfo in allProcesses)
    {
        if (String::IsNullOrEmpty(filterText) ||
            procInfo->Name->ToLower()->Contains(filterText))
        {
            comboBoxProcesses->Items->Add(procInfo);
        }
    }
}

System::Void MemoryReaderForm::Form_Resize(System::Object^ sender, System::EventArgs^ e)
{
    // 窗体大小改变时，控件会自动根据Anchor属性调整
    // 这里可以添加额外的布局调整逻辑
    this->Refresh();
}

System::Void MemoryReaderForm::btnClearBatch_Click(System::Object^ sender, System::EventArgs^ e)
{
    txtBatchAddresses->Clear();
    lastReadData->Clear();
    SetStatus("批量地址列表已清空");
}

System::Void MemoryReaderForm::chkContinuousRead_CheckedChanged(System::Object^ sender, System::EventArgs^ e)
{
    if (chkContinuousRead->Checked)
    {
        maxReadCount = (int)numReadCount->Value;
        currentReadCount = 0;
        lastReadData->Clear();

        if (maxReadCount == 0)
        {
            SetStatus("开始无限连续读取...");
        }
        else
        {
            SetStatus(String::Format("开始连续读取，共 {0} 次...", maxReadCount));
        }

        timerContinuousRead->Start();
        btnBatchRead->Enabled = false;
        numReadCount->Enabled = false;
    }
    else
    {
        timerContinuousRead->Stop();
        btnBatchRead->Enabled = true;
        numReadCount->Enabled = true;
        SetStatus("连续读取已停止");
    }
}

System::Void MemoryReaderForm::timerContinuousRead_Tick(System::Object^ sender, System::EventArgs^ e)
{
    // 检查是否达到最大读取次数
    if (maxReadCount > 0 && currentReadCount >= maxReadCount)
    {
        chkContinuousRead->Checked = false;
        return;
    }

    // 执行批量读取
    PerformBatchRead();
    currentReadCount++;

    if (maxReadCount > 0)
    {
        SetStatus(String::Format("连续读取进度: {0}/{1}", currentReadCount, maxReadCount));
    }
    else
    {
        SetStatus(String::Format("连续读取次数: {0}", currentReadCount));
    }
}

System::Void MemoryReaderForm::btnBatchRead_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("请先初始化驱动", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    ProcessInfo^ selectedProcess = dynamic_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    if (selectedProcess == nullptr)
    {
        MessageBox::Show("请先选择一个进程", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    String^ addressText = txtBatchAddresses->Text->Trim();
    if (String::IsNullOrEmpty(addressText))
    {
        MessageBox::Show("请输入要读取的地址列表", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    // 清空上次读取的数据
    lastReadData->Clear();

    // 执行单次批量读取
    PerformBatchRead();
}

void MemoryReaderForm::PerformBatchRead()
{
    ProcessInfo^ selectedProcess = dynamic_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    if (selectedProcess == nullptr || driver == nullptr)
        return;

    String^ addressText = txtBatchAddresses->Text->Trim();
    if (String::IsNullOrEmpty(addressText))
        return;

    try
    {
        // 分割地址行
        array<String^>^ lines = addressText->Split(gcnew array<wchar_t>{'\r', '\n'}, StringSplitOptions::RemoveEmptyEntries);

        System::Text::StringBuilder^ result = gcnew System::Text::StringBuilder();
        result->AppendLine("=== 批量内存读取结果 ===");
        result->AppendLine(String::Format("进程: {0} (PID: {1})", selectedProcess->Name, selectedProcess->PID));
        result->AppendLine(String::Format("读取时间: {0}", DateTime::Now.ToString()));
        if (chkContinuousRead->Checked)
        {
            result->AppendLine(String::Format("读取次数: {0}", currentReadCount + 1));
        }
        result->AppendLine();

        int successCount = 0;
        int totalCount = lines->Length;
        int changedCount = 0;

        for each (String^ line in lines)
        {
            String^ addr = line->Trim();
            if (String::IsNullOrEmpty(addr) || !addr->StartsWith("0x"))
                continue;

            try
            {
                // 解析地址
                uintptr_t address = Convert::ToUInt64(addr->Substring(2), 16);

                // 读取8字节数据
                array<System::Byte>^ data = gcnew array<System::Byte>(8);
                pin_ptr<System::Byte> pinnedData = &data[0];

                if (driver->read_virtual_memory(address, pinnedData, 8))
                {
                    bool hasChanged = false;
                    String^ changeInfo = "";

                    // 检查是否有上次的数据进行比较
                    if (lastReadData->ContainsKey(addr))
                    {
                        array<System::Byte>^ oldData = lastReadData[addr];
                        changeInfo = CompareAndHighlightChanges(data, oldData);

                        // 检查是否有变化
                        for (int i = 0; i < data->Length && i < oldData->Length; i++)
                        {
                            if (data[i] != oldData[i])
                            {
                                hasChanged = true;
                                break;
                            }
                        }

                        if (hasChanged) changedCount++;
                    }

                    // 更新存储的数据
                    array<System::Byte>^ dataCopy = gcnew array<System::Byte>(data->Length);
                    Array::Copy(data, dataCopy, data->Length);
                    lastReadData[addr] = dataCopy;

                    result->AppendLine(String::Format("地址 {0}{1}:", addr, hasChanged ? " [已变化]" : ""));
                    if (!String::IsNullOrEmpty(changeInfo))
                    {
                        result->AppendLine(String::Format("  变化对比: {0}", changeInfo));
                    }

                    // 显示十六进制数据
                    result->AppendLine(String::Format("  十六进制: {0}", ByteArrayToHexString(data)));

                    // 根据数据类型显示不同格式
                    String^ dataType = dynamic_cast<String^>(comboBoxDataType->SelectedItem);

                    if (dataType == "ASCII")
                    {
                        System::Text::StringBuilder^ ascii = gcnew System::Text::StringBuilder();
                        for (int i = 0; i < data->Length; i++)
                        {
                            if (data[i] >= 32 && data[i] <= 126)
                                ascii->Append((char)data[i]);
                            else
                                ascii->Append('.');
                        }
                        result->AppendLine(String::Format("  ASCII: {0}", ascii->ToString()));
                    }
                    else if (dataType == "Unicode")
                    {
                        if (data->Length >= 2)
                        {
                            System::Text::StringBuilder^ unicode = gcnew System::Text::StringBuilder();
                            for (int i = 0; i < data->Length - 1; i += 2)
                            {
                                System::UInt16 wch = BitConverter::ToUInt16(data, i);
                                if (wch >= 32 && wch <= 126)
                                    unicode->Append((char)wch);
                                else if (wch >= 0x4E00 && wch <= 0x9FFF)
                                    unicode->Append((wchar_t)wch);
                                else
                                    unicode->Append('.');
                            }
                            result->AppendLine(String::Format("  Unicode: {0}", unicode->ToString()));
                        }
                    }
                    else if (dataType == "指针")
                    {
                        uintptr_t ptr = BitConverter::ToUInt64(data, 0);
                        if (ptr != 0)
                        {
                            result->AppendLine(String::Format("  指针: 0x{0:X16} ({1})", ptr, ptr));
                        }
                        else
                        {
                            result->AppendLine("  指针: NULL");
                        }
                    }
                    else if (dataType == "Int8")
                    {
                        result->AppendLine(String::Format("  Int8: {0}", (System::SByte)data[0]));
                    }
                    else if (dataType == "UInt8")
                    {
                        result->AppendLine(String::Format("  UInt8: {0}", data[0]));
                    }
                    else if (dataType == "Int16")
                    {
                        result->AppendLine(String::Format("  Int16: {0}", BitConverter::ToInt16(data, 0)));
                    }
                    else if (dataType == "UInt16")
                    {
                        result->AppendLine(String::Format("  UInt16: {0}", BitConverter::ToUInt16(data, 0)));
                    }
                    else if (dataType == "Int32")
                    {
                        result->AppendLine(String::Format("  Int32: {0}", BitConverter::ToInt32(data, 0)));
                    }
                    else if (dataType == "UInt32")
                    {
                        result->AppendLine(String::Format("  UInt32: {0}", BitConverter::ToUInt32(data, 0)));
                    }
                    else if (dataType == "Int64")
                    {
                        result->AppendLine(String::Format("  Int64: {0}", BitConverter::ToInt64(data, 0)));
                    }
                    else if (dataType == "UInt64")
                    {
                        result->AppendLine(String::Format("  UInt64: {0}", BitConverter::ToUInt64(data, 0)));
                    }
                    else if (dataType == "Float")
                    {
                        result->AppendLine(String::Format("  Float: {0}", BitConverter::ToSingle(data, 0)));
                    }
                    else if (dataType == "Double")
                    {
                        result->AppendLine(String::Format("  Double: {0}", BitConverter::ToDouble(data, 0)));
                    }
                    else // 默认为十六进制，显示所有类型
                    {
                        result->AppendLine(String::Format("  Int64: {0}", BitConverter::ToInt64(data, 0)));
                        result->AppendLine(String::Format("  UInt64: {0}", BitConverter::ToUInt64(data, 0)));
                        result->AppendLine(String::Format("  Double: {0}", BitConverter::ToDouble(data, 0)));
                    }

                    result->AppendLine();
                    successCount++;
                }
                else
                {
                    result->AppendLine(String::Format("地址 {0}: 读取失败", addr));
                    result->AppendLine();
                }
            }
            catch (...)
            {
                result->AppendLine(String::Format("地址 {0}: 地址格式错误", addr));
                result->AppendLine();
            }
        }

        String^ summary = String::Format("=== 批量读取完成: {0}/{1} 成功", successCount, totalCount);
        if (changedCount > 0)
        {
            summary += String::Format(", {0} 个地址数据已变化", changedCount);
        }
        summary += " ===";
        result->AppendLine(summary);

        txtOutput->Text = result->ToString();

        if (!chkContinuousRead->Checked)
        {
            SetStatus(String::Format("批量读取完成: {0}/{1} 成功, {2} 个变化", successCount, totalCount, changedCount));
        }
    }
    catch (const std::exception& ex)
    {
        String^ errorMsg = gcnew String(ex.what());
        if (!chkContinuousRead->Checked)
        {
            MessageBox::Show("批量读取失败: " + errorMsg, "错误",
                            MessageBoxButtons::OK, MessageBoxIcon::Error);
        }
        SetStatus("批量读取失败");
    }
}

String^ MemoryReaderForm::CompareAndHighlightChanges(array<System::Byte>^ newData, array<System::Byte>^ oldData)
{
    if (newData == nullptr || oldData == nullptr)
        return "";

    System::Text::StringBuilder^ result = gcnew System::Text::StringBuilder();
    int minLength = Math::Min(newData->Length, oldData->Length);

    for (int i = 0; i < minLength; i++)
    {
        if (newData[i] != oldData[i])
        {
            if (result->Length > 0)
                result->Append(" ");
            result->AppendFormat("[{0}]: {1:X2}→{2:X2}", i, oldData[i], newData[i]);
        }
    }

    return result->ToString();
}

System::Void MemoryReaderForm::btnContinuousRead_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("请先初始化驱动", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    ProcessInfo^ selectedProcess = dynamic_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    if (selectedProcess == nullptr)
    {
        MessageBox::Show("请先选择一个进程", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    String^ addressText = txtAddress->Text->Trim();
    if (String::IsNullOrEmpty(addressText) || !addressText->StartsWith("0x"))
    {
        MessageBox::Show("请输入有效的地址 (格式: 0x1234567890ABCDEF)", "警告",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    if (timerSingleRead->Enabled)
    {
        // 停止连续读取
        timerSingleRead->Stop();
        btnContinuousRead->Text = L"连续读取";
        btnReadMemory->Enabled = true;
        numSingleReadCount->Enabled = true;
        txtAddress->Enabled = true;
        numSize->Enabled = true;
        SetStatus("连续读取已停止");
    }
    else
    {
        // 开始连续读取
        singleMaxReadCount = (int)numSingleReadCount->Value;
        singleCurrentReadCount = 0;
        lastSingleReadData = nullptr;

        if (singleMaxReadCount == 0)
        {
            SetStatus("开始无限连续读取...");
        }
        else
        {
            SetStatus(String::Format("开始连续读取，共 {0} 次...", singleMaxReadCount));
        }

        timerSingleRead->Start();
        btnContinuousRead->Text = L"停止读取";
        btnReadMemory->Enabled = false;
        numSingleReadCount->Enabled = false;
        txtAddress->Enabled = false;
        numSize->Enabled = false;
    }
}

System::Void MemoryReaderForm::timerSingleRead_Tick(System::Object^ sender, System::EventArgs^ e)
{
    // 检查是否达到最大读取次数
    if (singleMaxReadCount > 0 && singleCurrentReadCount >= singleMaxReadCount)
    {
        timerSingleRead->Stop();
        btnContinuousRead->Text = L"连续读取";
        btnReadMemory->Enabled = true;
        numSingleReadCount->Enabled = true;
        txtAddress->Enabled = true;
        numSize->Enabled = true;
        SetStatus("连续读取完成");
        return;
    }

    // 执行单次读取
    PerformSingleRead();
    singleCurrentReadCount++;

    if (singleMaxReadCount > 0)
    {
        SetStatus(String::Format("连续读取进度: {0}/{1}", singleCurrentReadCount, singleMaxReadCount));
    }
    else
    {
        SetStatus(String::Format("连续读取次数: {0}", singleCurrentReadCount));
    }
}

void MemoryReaderForm::PerformSingleRead()
{
    ProcessInfo^ selectedProcess = dynamic_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    if (selectedProcess == nullptr || driver == nullptr)
        return;

    String^ addressText = txtAddress->Text->Trim();
    if (String::IsNullOrEmpty(addressText) || !addressText->StartsWith("0x"))
        return;

    try
    {
        // 解析地址
        uintptr_t address = Convert::ToUInt64(addressText->Substring(2), 16);
        int size = (int)numSize->Value;

        // 读取内存数据
        array<System::Byte>^ data = gcnew array<System::Byte>(size);
        pin_ptr<System::Byte> pinnedData = &data[0];

        if (driver->read_virtual_memory(address, pinnedData, size))
        {
            bool hasChanged = false;
            String^ changeInfo = "";

            // 检查是否有上次的数据进行比较
            if (lastSingleReadData != nullptr && timerSingleRead->Enabled)
            {
                changeInfo = CompareSingleReadChanges(data, lastSingleReadData);

                // 检查是否有变化
                for (int i = 0; i < data->Length && i < lastSingleReadData->Length; i++)
                {
                    if (data[i] != lastSingleReadData[i])
                    {
                        hasChanged = true;
                        break;
                    }
                }
            }

            // 更新存储的数据
            array<System::Byte>^ dataCopy = gcnew array<System::Byte>(data->Length);
            Array::Copy(data, dataCopy, data->Length);
            lastSingleReadData = dataCopy;

            // 构建输出结果
            System::Text::StringBuilder^ result = gcnew System::Text::StringBuilder();
            result->AppendLine("=== 单个地址读取结果 ===");
            result->AppendLine(String::Format("进程: {0} (PID: {1})", selectedProcess->Name, selectedProcess->PID));
            result->AppendLine(String::Format("地址: {0}", addressText));
            result->AppendLine(String::Format("大小: {0} 字节", size));
            result->AppendLine(String::Format("读取时间: {0}", DateTime::Now.ToString()));
            if (timerSingleRead->Enabled)
            {
                result->AppendLine(String::Format("读取次数: {0}", singleCurrentReadCount + 1));
                if (hasChanged)
                {
                    result->AppendLine("*** 数据已变化 ***");
                    if (!String::IsNullOrEmpty(changeInfo))
                    {
                        result->AppendLine(String::Format("变化详情: {0}", changeInfo));
                    }
                }
            }
            result->AppendLine();

            // 显示数据
            result->AppendLine("十六进制数据:");
            result->AppendLine(ByteArrayToHexString(data));
            result->AppendLine();

            // 根据数据类型显示不同格式
            String^ dataType = dynamic_cast<String^>(comboBoxDataType->SelectedItem);

            if (dataType == "ASCII")
            {
                result->AppendLine("ASCII 解释:");
                System::Text::StringBuilder^ ascii = gcnew System::Text::StringBuilder();
                for (int i = 0; i < data->Length; i++)
                {
                    if (data[i] >= 32 && data[i] <= 126)
                        ascii->Append((char)data[i]);
                    else
                        ascii->Append('.');
                }
                result->AppendLine(ascii->ToString());
            }
            else if (dataType == "Unicode")
            {
                result->AppendLine("Unicode 解释:");
                if (data->Length >= 2)
                {
                    System::Text::StringBuilder^ unicode = gcnew System::Text::StringBuilder();
                    for (int i = 0; i < data->Length - 1; i += 2)
                    {
                        System::UInt16 wch = BitConverter::ToUInt16(data, i);
                        if (wch >= 32 && wch <= 126)
                            unicode->Append((char)wch);
                        else if (wch >= 0x4E00 && wch <= 0x9FFF) // 中文字符范围
                            unicode->Append((wchar_t)wch);
                        else
                            unicode->Append('.');
                    }
                    result->AppendLine(unicode->ToString());
                }
            }
            else if (dataType == "指针")
            {
                result->AppendLine("指针解释:");
                if (data->Length >= 8)
                {
                    uintptr_t ptr = BitConverter::ToUInt64(data, 0);
                    result->AppendLine(String::Format("  指针值: 0x{0:X16}", ptr));
                    if (ptr != 0)
                    {
                        result->AppendLine(String::Format("  十进制: {0}", ptr));
                    }
                    else
                    {
                        result->AppendLine("  空指针 (NULL)");
                    }
                }
                else if (data->Length >= 4)
                {
                    uint32_t ptr = BitConverter::ToUInt32(data, 0);
                    result->AppendLine(String::Format("  指针值: 0x{0:X8}", ptr));
                    if (ptr != 0)
                    {
                        result->AppendLine(String::Format("  十进制: {0}", ptr));
                    }
                    else
                    {
                        result->AppendLine("  空指针 (NULL)");
                    }
                }
            }
            else if (dataType == "Int8")
            {
                result->AppendLine("Int8 解释:");
                for (int i = 0; i < data->Length; i++)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, (System::SByte)data[i]));
                }
            }
            else if (dataType == "UInt8")
            {
                result->AppendLine("UInt8 解释:");
                for (int i = 0; i < data->Length; i++)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, data[i]));
                }
            }
            else if (dataType == "Int16")
            {
                result->AppendLine("Int16 解释:");
                for (int i = 0; i < data->Length - 1; i += 2)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToInt16(data, i)));
                }
            }
            else if (dataType == "UInt16")
            {
                result->AppendLine("UInt16 解释:");
                for (int i = 0; i < data->Length - 1; i += 2)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToUInt16(data, i)));
                }
            }
            else if (dataType == "Int32")
            {
                result->AppendLine("Int32 解释:");
                for (int i = 0; i < data->Length - 3; i += 4)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToInt32(data, i)));
                }
            }
            else if (dataType == "UInt32")
            {
                result->AppendLine("UInt32 解释:");
                for (int i = 0; i < data->Length - 3; i += 4)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToUInt32(data, i)));
                }
            }
            else if (dataType == "Int64")
            {
                result->AppendLine("Int64 解释:");
                for (int i = 0; i < data->Length - 7; i += 8)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToInt64(data, i)));
                }
            }
            else if (dataType == "UInt64")
            {
                result->AppendLine("UInt64 解释:");
                for (int i = 0; i < data->Length - 7; i += 8)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToUInt64(data, i)));
                }
            }
            else if (dataType == "Float")
            {
                result->AppendLine("Float 解释:");
                for (int i = 0; i < data->Length - 3; i += 4)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToSingle(data, i)));
                }
            }
            else if (dataType == "Double")
            {
                result->AppendLine("Double 解释:");
                for (int i = 0; i < data->Length - 7; i += 8)
                {
                    result->AppendLine(String::Format("  [偏移{0}]: {1}", i, BitConverter::ToDouble(data, i)));
                }
            }
            else // 默认为十六进制
            {
                result->AppendLine("数值解释 (所有类型):");

                // 8字节数据类型
                if (data->Length >= 8)
                {
                    result->AppendLine(String::Format("  Int64:    {0}", BitConverter::ToInt64(data, 0)));
                    result->AppendLine(String::Format("  UInt64:   {0}", BitConverter::ToUInt64(data, 0)));
                    result->AppendLine(String::Format("  Double:   {0}", BitConverter::ToDouble(data, 0)));
                }

                // 4字节数据类型
                if (data->Length >= 4)
                {
                    result->AppendLine(String::Format("  Int32:    {0}", BitConverter::ToInt32(data, 0)));
                    result->AppendLine(String::Format("  UInt32:   {0}", BitConverter::ToUInt32(data, 0)));
                    result->AppendLine(String::Format("  Float:    {0}", BitConverter::ToSingle(data, 0)));
                }

                // 2字节数据类型
                if (data->Length >= 2)
                {
                    result->AppendLine(String::Format("  Int16:    {0}", BitConverter::ToInt16(data, 0)));
                    result->AppendLine(String::Format("  UInt16:   {0}", BitConverter::ToUInt16(data, 0)));
                }

                // 1字节数据类型
                if (data->Length >= 1)
                {
                    result->AppendLine(String::Format("  Byte:     {0}", data[0]));
                    result->AppendLine(String::Format("  SByte:    {0}", (System::SByte)data[0]));
                }
            }

            txtOutput->Text = result->ToString();

            if (!timerSingleRead->Enabled)
            {
                SetStatus("内存读取成功");
            }
        }
        else
        {
            txtOutput->Text = "读取失败: 无法读取指定地址的内存";
            if (!timerSingleRead->Enabled)
            {
                SetStatus("内存读取失败");
            }
        }
    }
    catch (...)
    {
        txtOutput->Text = "读取失败: 地址格式错误或其他异常";
        if (!timerSingleRead->Enabled)
        {
            SetStatus("内存读取失败");
        }
    }
}

String^ MemoryReaderForm::CompareSingleReadChanges(array<System::Byte>^ newData, array<System::Byte>^ oldData)
{
    if (newData == nullptr || oldData == nullptr)
        return "";

    System::Text::StringBuilder^ result = gcnew System::Text::StringBuilder();
    int minLength = Math::Min(newData->Length, oldData->Length);
    int changeCount = 0;

    for (int i = 0; i < minLength; i++)
    {
        if (newData[i] != oldData[i])
        {
            if (changeCount > 0)
                result->Append(", ");
            result->AppendFormat("偏移{0}: {1:X2}→{2:X2}", i, oldData[i], newData[i]);
            changeCount++;

            // 限制显示的变化数量，避免输出过长
            if (changeCount >= 10)
            {
                result->Append("...");
                break;
            }
        }
    }

    return result->ToString();
}