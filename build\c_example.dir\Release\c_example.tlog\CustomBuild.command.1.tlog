^C:\USERS\<USER>\DESKTOP\驱动漏洞\DLLTEST\TEST\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/驱动漏洞/dlltest/test -BC:/Users/<USER>/Desktop/驱动漏洞/dlltest/test/build --check-stamp-file C:/Users/<USER>/Desktop/驱动漏洞/dlltest/test/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
